package com.anginatech.textrepeater;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

import org.json.JSONObject;
import java.util.Random;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Modern Ads Helper with seamless user experience
 * Features:
 * - Background ad preloading for instant display
 * - No ad break periods or forced delays
 * - Continuous ad refresh and availability
 * - Silent failure handling
 * - Optimized for smooth user experience
 */
public class AdsHelper {
    private static final String TAG = "ModernAdsHelper";

    // Ad configuration
    public static String admob_banner = "";
    public static String admob_interstitial = "";
    public static String admob_app_open = "";
    public static boolean isAds = false;

    // Modern ad management
    private static final ConcurrentLinkedQueue<InterstitialAd> interstitialAdQueue = new ConcurrentLinkedQueue<>();
    private static final ConcurrentLinkedQueue<AdView> bannerAdQueue = new ConcurrentLinkedQueue<>();
    private static final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private static final AtomicBoolean isPreloadingActive = new AtomicBoolean(false);

    // Background services
    private static ScheduledExecutorService backgroundExecutor;
    private static Handler mainHandler;

    // Preloading configuration
    private static final int MAX_PRELOADED_INTERSTITIALS = 3;
    private static final int MAX_PRELOADED_BANNERS = 2;
    private static final long PRELOAD_INTERVAL_MIN = 30000; // 30 seconds
    private static final long PRELOAD_INTERVAL_MAX = 120000; // 2 minutes

    /**
     * Initialize the modern ads system with background preloading
     */
    public static void initializeAds(Context context) {
        if (isInitialized.getAndSet(true)) {
            Log.d(TAG, "Ads system already initialized");
            return;
        }

        Log.d(TAG, "Initializing modern ads system...");

        // Initialize main handler
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }

        // Initialize background executor
        if (backgroundExecutor == null) {
            backgroundExecutor = Executors.newScheduledThreadPool(2);
        }

        // Load configuration immediately from cache
        loadAdConfigFromPrefs(context);

        // Start background configuration loading
        backgroundExecutor.execute(() -> loadAdsConfiguration(context));

        // Start background preloading system
        startBackgroundPreloading(context);

        Log.d(TAG, "Modern ads system initialized successfully");
    }

    /**
     * Start the background ad preloading system
     */
    private static void startBackgroundPreloading(Context context) {
        if (isPreloadingActive.getAndSet(true)) {
            return;
        }

        Log.d(TAG, "Starting background ad preloading system");

        // Schedule continuous preloading with random intervals
        backgroundExecutor.scheduleWithFixedDelay(() -> {
            try {
                if (isAds && context != null) {
                    preloadInterstitialAds(context);
                    preloadBannerAds(context);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in background preloading: " + e.getMessage());
            }
        }, 5, getRandomPreloadInterval(), TimeUnit.MILLISECONDS);
    }

    /**
     * Get random interval for preloading to avoid predictable patterns
     */
    private static long getRandomPreloadInterval() {
        Random random = new Random();
        return PRELOAD_INTERVAL_MIN + random.nextLong() % (PRELOAD_INTERVAL_MAX - PRELOAD_INTERVAL_MIN);
    }

    /**
     * Load ads configuration from server with silent error handling
     */
    private static void loadAdsConfiguration(Context context) {
        Log.d(TAG, "Loading ads configuration...");

        ApiClient apiClient = ApiClient.getInstance(context);
        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                try {
                    // Parse API response
                    admob_banner = config.optString("banner_id", "");
                    admob_interstitial = config.optString("interstitial_id", "");
                    admob_app_open = config.optString("app_open_id", "");
                    isAds = config.optInt("is_active", 1) == 1;

                    Log.d(TAG, "Ad configuration loaded successfully - Ads enabled: " + isAds);

                    // Save to cache for offline use
                    saveAdConfigToPrefs(context, config);

                    // Start preloading if ads are enabled
                    if (isAds) {
                        backgroundExecutor.execute(() -> {
                            preloadInterstitialAds(context);
                            preloadBannerAds(context);
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing API response: " + e.getMessage());
                    loadAdsConfigurationFallback(context);
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading ads configuration: " + error);
                loadAdsConfigurationFallback(context);
            }
        });
    }

    /**
     * Fallback method to load ads configuration from old API
     */
    private static void loadAdsConfigurationFallback(Context context) {
        Log.d(TAG, "Loading ads configuration from fallback API...");

        RequestQueue queue = Volley.newRequestQueue(context);
        String fallbackUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_CONFIG);
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET, fallbackUrl, null,
                response -> {
                    try {
                        if (response.has("banner_id") && !response.getString("banner_id").isEmpty()) {
                            admob_banner = response.getString("banner_id");
                        }
                        if (response.has("interstitial_id") && !response.getString("interstitial_id").isEmpty()) {
                            admob_interstitial = response.getString("interstitial_id");
                        }
                        if (response.has("app_open_id") && !response.getString("app_open_id").isEmpty()) {
                            admob_app_open = response.getString("app_open_id");
                        }
                        isAds = response.getInt("is_active") == 1;

                        Log.d(TAG, "Fallback configuration loaded - Ads enabled: " + isAds);

                        // Start preloading if ads are enabled
                        if (isAds) {
                            backgroundExecutor.execute(() -> {
                                preloadInterstitialAds(context);
                                preloadBannerAds(context);
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing fallback response: " + e.getMessage());
                        // Silent failure - continue with cached config
                    }
                },
                error -> {
                    Log.e(TAG, "Fallback API failed: " + error.getMessage());
                    // Silent failure - continue with cached config
                });
        queue.add(request);
    }

    /**
     * Save ad configuration to SharedPreferences for offline use
     */
    private static void saveAdConfigToPrefs(Context context, JSONObject config) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();

            editor.putString(Config.PREF_BANNER_AD_ID, config.optString("banner_id", ""));
            editor.putString(Config.PREF_INTERSTITIAL_AD_ID, config.optString("interstitial_id", ""));
            editor.putString(Config.PREF_APP_OPEN_AD_ID, config.optString("app_open_id", ""));
            editor.putBoolean(Config.PREF_ADMOB_ENABLED, config.optInt("is_active", 1) == 1);
            editor.putLong("last_updated", System.currentTimeMillis());
            editor.apply();

            Log.d(TAG, "Ad configuration cached successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error saving ad config: " + e.getMessage());
        }
    }

    /**
     * Load ad configuration from cache
     */
    private static void loadAdConfigFromPrefs(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);

            admob_banner = prefs.getString(Config.PREF_BANNER_AD_ID, "");
            admob_interstitial = prefs.getString(Config.PREF_INTERSTITIAL_AD_ID, "");
            admob_app_open = prefs.getString(Config.PREF_APP_OPEN_AD_ID, "");
            isAds = prefs.getBoolean(Config.PREF_ADMOB_ENABLED, Config.DEFAULT_ADMOB_ENABLED);

            Log.d(TAG, "Ad configuration loaded from cache - Ads enabled: " + isAds);
        } catch (Exception e) {
            Log.e(TAG, "Error loading cached config: " + e.getMessage());
            // Set safe defaults
            isAds = false;
        }
    }

    /**
     * Check if interstitial ad is available for instant display
     */
    public static boolean isInterstitialAdLoaded() {
        return !interstitialAdQueue.isEmpty();
    }

    /**
     * Show interstitial ad instantly with seamless experience
     */
    public static void showInterstitialAd(Activity activity, FullScreenContentCallback userCallback) {
        InterstitialAd ad = interstitialAdQueue.poll();

        if (ad != null) {
            Log.d(TAG, "Showing preloaded interstitial ad instantly");

            // Set up callback wrapper for seamless experience
            FullScreenContentCallback wrapperCallback = new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed");

                    // Trigger background preloading for next ad
                    backgroundExecutor.execute(() -> preloadInterstitialAds(activity));

                    // Call user callback
                    if (userCallback != null) {
                        userCallback.onAdDismissedFullScreenContent();
                    }
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());

                    // Trigger background preloading
                    backgroundExecutor.execute(() -> preloadInterstitialAds(activity));

                    // Call user callback
                    if (userCallback != null) {
                        userCallback.onAdFailedToShowFullScreenContent(adError);
                    }
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad displayed successfully");
                    if (userCallback != null) {
                        userCallback.onAdShowedFullScreenContent();
                    }
                }
            };

            ad.setFullScreenContentCallback(wrapperCallback);
            ad.show(activity);
        } else {
            Log.d(TAG, "No preloaded interstitial ad available");
            // Trigger immediate preloading for future use
            backgroundExecutor.execute(() -> preloadInterstitialAds(activity));

            // Call user callback to indicate no ad was shown
            if (userCallback != null) {
                userCallback.onAdFailedToShowFullScreenContent(null);
            }
        }
    }

    /**
     * Show interstitial ad with default callback
     */
    public static void showInterstitialAd(Activity activity) {
        showInterstitialAd(activity, null);
    }

    /**
     * Preload interstitial ads in background for instant display
     */
    private static void preloadInterstitialAds(Context context) {
        if (!isAds || admob_interstitial.isEmpty()) {
            return;
        }

        // Only preload if we need more ads
        if (interstitialAdQueue.size() >= MAX_PRELOADED_INTERSTITIALS) {
            return;
        }

        Log.d(TAG, "Preloading interstitial ads in background");

        try {
            AdRequest adRequest = new AdRequest.Builder().build();

            // Load on main thread as required by AdMob
            if (mainHandler != null) {
                mainHandler.post(() -> {
                    InterstitialAd.load(context, admob_interstitial, adRequest,
                            new InterstitialAdLoadCallback() {
                                @Override
                                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                                    // Add to queue for instant display
                                    interstitialAdQueue.offer(interstitialAd);
                                    Log.d(TAG, "Interstitial ad preloaded successfully. Queue size: " +
                                           interstitialAdQueue.size());
                                }

                                @Override
                                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                                    Log.e(TAG, "Failed to preload interstitial ad: " + loadAdError.getMessage());
                                    // Silent failure - retry later in background
                                    backgroundExecutor.schedule(() -> {
                                        preloadInterstitialAds(context);
                                    }, getRandomPreloadInterval(), TimeUnit.MILLISECONDS);
                                }
                            });
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error preloading interstitial ads: " + e.getMessage());
        }
    }

    /**
     * Legacy method for compatibility - now uses modern preloading
     */
    public static void loadInterstitialAd(Context context) {
        backgroundExecutor.execute(() -> preloadInterstitialAds(context));
    }

    /**
     * Preload banner ads in background for instant display
     */
    private static void preloadBannerAds(Context context) {
        if (!isAds || admob_banner.isEmpty()) {
            return;
        }

        // Only preload if we need more ads
        if (bannerAdQueue.size() >= MAX_PRELOADED_BANNERS) {
            return;
        }

        Log.d(TAG, "Preloading banner ads in background");

        try {
            if (mainHandler != null) {
                mainHandler.post(() -> {
                    try {
                        AdView adView = new AdView(context);
                        adView.setAdSize(AdSize.BANNER);
                        adView.setAdUnitId(admob_banner);

                        // Set up listener for preloading
                        adView.setAdListener(new AdListener() {
                            @Override
                            public void onAdLoaded() {
                                // Add to queue for instant display
                                bannerAdQueue.offer(adView);
                                Log.d(TAG, "Banner ad preloaded successfully. Queue size: " +
                                       bannerAdQueue.size());
                            }

                            @Override
                            public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                                Log.e(TAG, "Failed to preload banner ad: " + loadAdError.getMessage());
                                // Silent failure - retry later
                                backgroundExecutor.schedule(() -> {
                                    preloadBannerAds(context);
                                }, getRandomPreloadInterval(), TimeUnit.MILLISECONDS);
                            }
                        });

                        AdRequest adRequest = new AdRequest.Builder().build();
                        adView.loadAd(adRequest);
                    } catch (Exception e) {
                        Log.e(TAG, "Error creating banner ad: " + e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error preloading banner ads: " + e.getMessage());
        }
    }

    /**
     * Load banner ad instantly from preloaded queue
     */
    public static void loadBannerAd(Activity activity, ViewGroup adContainer) {
        if (!isAds || adContainer == null || admob_banner.isEmpty()) {
            Log.d(TAG, "Banner ads disabled or container unavailable");
            if (adContainer != null) {
                adContainer.removeAllViews();
                adContainer.setVisibility(View.GONE);
            }
            return;
        }

        // Try to get preloaded banner ad for instant display
        AdView preloadedAd = bannerAdQueue.poll();

        if (preloadedAd != null) {
            Log.d(TAG, "Displaying preloaded banner ad instantly");

            try {
                // Remove from any previous parent
                if (preloadedAd.getParent() != null) {
                    ((ViewGroup) preloadedAd.getParent()).removeView(preloadedAd);
                }

                // Add to container and show
                adContainer.removeAllViews();
                adContainer.addView(preloadedAd);
                adContainer.setVisibility(View.VISIBLE);

                // Set up click listener for seamless experience
                preloadedAd.setAdListener(new AdListener() {
                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "Banner ad clicked");
                        // Trigger background preloading for next ad
                        backgroundExecutor.execute(() -> preloadBannerAds(activity));
                    }
                });

                // Trigger background preloading for next ad
                backgroundExecutor.execute(() -> preloadBannerAds(activity));

            } catch (Exception e) {
                Log.e(TAG, "Error displaying preloaded banner: " + e.getMessage());
                // Fallback to loading new ad
                loadBannerAdFallback(activity, adContainer);
            }
        } else {
            Log.d(TAG, "No preloaded banner available, loading new ad");
            loadBannerAdFallback(activity, adContainer);
        }
    }

    /**
     * Fallback method to load banner ad when no preloaded ad is available
     */
    private static void loadBannerAdFallback(Activity activity, ViewGroup adContainer) {
        try {
            AdView adView = new AdView(activity);
            adView.setAdSize(AdSize.BANNER);
            adView.setAdUnitId(admob_banner);

            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    Log.d(TAG, "Fallback banner ad loaded");
                    adContainer.setVisibility(View.VISIBLE);
                    // Trigger background preloading
                    backgroundExecutor.execute(() -> preloadBannerAds(activity));
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Fallback banner ad failed to load: " + loadAdError.getMessage());
                    adContainer.setVisibility(View.GONE);
                    // Retry preloading in background
                    backgroundExecutor.execute(() -> preloadBannerAds(activity));
                }
            });

            adContainer.removeAllViews();
            adContainer.addView(adView);

            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);

        } catch (Exception e) {
            Log.e(TAG, "Error in fallback banner loading: " + e.getMessage());
            adContainer.setVisibility(View.GONE);
        }
    }

    /**
     * Refresh ads when returning to MainActivity for seamless experience
     */
    public static void refreshAdsOnReturn(Context context) {
        if (!isAds) {
            return;
        }

        Log.d(TAG, "Refreshing ads on return to MainActivity");

        // Trigger background preloading to ensure fresh ads are available
        backgroundExecutor.execute(() -> {
            preloadInterstitialAds(context);
            preloadBannerAds(context);
        });
    }

    /**
     * Clean up resources when app is destroyed
     */
    public static void cleanup() {
        Log.d(TAG, "Cleaning up ads system resources");

        try {
            // Clear ad queues
            interstitialAdQueue.clear();
            bannerAdQueue.clear();

            // Shutdown background executor
            if (backgroundExecutor != null && !backgroundExecutor.isShutdown()) {
                backgroundExecutor.shutdown();
                try {
                    if (!backgroundExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        backgroundExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    backgroundExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            // Reset state
            isInitialized.set(false);
            isPreloadingActive.set(false);

        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup: " + e.getMessage());
        }
    }

    /**
     * Get current ad queue status for debugging
     */
    public static String getAdQueueStatus() {
        return String.format("Interstitial queue: %d, Banner queue: %d, Preloading active: %s",
                interstitialAdQueue.size(), bannerAdQueue.size(), isPreloadingActive.get());
    }
}
